# URWAY Payment Gateway Integration Guide

## Overview
This guide covers the complete integration of URWAY Payment Gateway into the Gather Point application for handling property reservation payments.

## Backend Setup

### 1. Database Migration
Run the following migrations to set up the payment system:

```bash
php artisan migrate
```

This will create:
- `payment_gateway_settings` table (with terminal_password field)
- `payment_transactions` table

### 2. Seed URWAY Configuration
Run the seeder to populate URWAY configuration:

```bash
php artisan db:seed --class=UrwayPaymentGatewaySeeder
```

This will configure URWAY with the provided test credentials:
- Terminal ID: `gatherpoin`
- Terminal Password: `gather@4512`
- Merchant Key: `75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef`
- Base URL: `https://payments-dev.urway-tech.com` (Test environment)

### 3. Environment Configuration
Add the following to your `.env` file:

```env
# URWAY Payment Gateway
URWAY_TERMINAL_ID=gatherpoin
URWAY_TERMINAL_PASSWORD=gather@4512
URWAY_MERCHANT_KEY=75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef
URWAY_BASE_URL=https://payments-dev.urway-tech.com
```

## Frontend Setup

### 1. Install Dependencies
Add the webview_flutter package to pubspec.yaml:

```yaml
dependencies:
  webview_flutter: ^4.4.2
```

Run:
```bash
flutter pub get
```

### 2. Service Locator Registration
Add payment services to your service locator:

```dart
// In your service locator setup
getIt.registerLazySingleton<PaymentApiService>(
  () => PaymentApiService(getIt<DioConsumer>()),
);

getIt.registerLazySingleton<UrwayPaymentService>(
  () => UrwayPaymentService(getIt<PaymentApiService>()),
);
```

## API Endpoints

### Payment Endpoints
- `POST /api/payments/initiate` - Initiate payment
- `POST /api/payments/status` - Check payment status
- `POST /api/payments/urway/callback` - URWAY webhook (no auth required)

### Request/Response Examples

#### Initiate Payment
```json
// Request
{
  "reservation_id": 123,
  "amount": 500.00,
  "customer_email": "<EMAIL>"
}

// Response
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "payment_id": "GP_64F8A2B1C3D4E",
    "payment_url": "https://payments-dev.urway-tech.com/...",
    "amount": 500.00,
    "currency": "SAR"
  }
}
```

#### Check Payment Status
```json
// Request
{
  "payment_id": "GP_64F8A2B1C3D4E"
}

// Response
{
  "success": true,
  "data": {
    "payment_id": "GP_64F8A2B1C3D4E",
    "status": "completed",
    "amount": 500.00,
    "currency": "SAR",
    "paid_at": "2025-06-23T10:30:00Z"
  }
}
```

## Payment Flow

### 1. Reservation Creation
1. User selects dates and property details
2. App calls `/api/reservations/create` to create reservation
3. Reservation is created with `confirmed: false`

### 2. Payment Initiation
1. User proceeds to payment screen
2. App calls `/api/payments/initiate` with reservation details
3. Backend creates PaymentTransaction record
4. Backend calls URWAY API to get payment URL
5. App opens payment URL in webview

### 3. Payment Processing
1. User completes payment in URWAY webview
2. URWAY sends callback to `/api/payments/urway/callback`
3. Backend verifies payment and updates transaction status
4. If successful, reservation is confirmed and host earning is created

### 4. Payment Completion
1. App polls payment status or receives webview completion
2. App navigates to success/failure screen based on status
3. User can view booking details or return to home

## Testing

### Test Cards for URWAY
Use these test card numbers in the URWAY test environment:

**Successful Payments:**
- Card Number: `****************`
- Expiry: Any future date
- CVV: Any 3 digits

**Failed Payments:**
- Card Number: `****************`
- Expiry: Any future date
- CVV: Any 3 digits

### Testing Checklist

#### Backend Testing
- [ ] Payment gateway configuration is loaded correctly
- [ ] Payment initiation creates transaction record
- [ ] URWAY API integration works with test credentials
- [ ] Webhook handling processes callbacks correctly
- [ ] Reservation confirmation works after payment
- [ ] Host earnings are created correctly
- [ ] Error handling works for failed payments

#### Frontend Testing
- [ ] Payment screen displays correctly with MADA logo positioning
- [ ] Webview opens URWAY payment page
- [ ] Payment success navigation works
- [ ] Payment failure handling works
- [ ] Payment cancellation handling works
- [ ] Loading states display correctly
- [ ] Error messages are user-friendly

#### Integration Testing
- [ ] Complete reservation-to-payment flow works
- [ ] Payment status polling works correctly
- [ ] Webhook and polling don't conflict
- [ ] Database transactions are atomic
- [ ] Payment retry functionality works
- [ ] Navigation flow is intuitive

## MADA Logo Requirements

As per URWAY requirements, the MADA logo must be positioned:
- **English**: First position (leftmost)
- **Arabic**: First position (rightmost)
- **Size**: Same as other payment method logos

The implementation in `PaymentScreen` follows these requirements.

## Security Considerations

1. **Hash Verification**: All URWAY callbacks are verified using SHA256 hash
2. **HTTPS Only**: All payment URLs use HTTPS
3. **Token Validation**: API endpoints require valid authentication
4. **Input Validation**: All payment amounts and IDs are validated
5. **Error Logging**: Payment errors are logged for monitoring

## Production Deployment

### 1. Update URWAY Configuration
Change the base URL to production:
```env
URWAY_BASE_URL=https://payments.urway-tech.com
```

### 2. SSL Certificate
Ensure your callback URL has a valid SSL certificate.

### 3. Webhook URL
Update the callback URL in URWAY dashboard to:
```
https://yourdomain.com/api/payments/urway/callback
```

### 4. Monitoring
Set up monitoring for:
- Payment success/failure rates
- URWAY API response times
- Webhook delivery failures
- Database transaction integrity

## Troubleshooting

### Common Issues

1. **Payment URL not loading**
   - Check URWAY credentials
   - Verify network connectivity
   - Check URWAY service status

2. **Webhook not received**
   - Verify callback URL is accessible
   - Check SSL certificate
   - Review URWAY dashboard settings

3. **Payment status not updating**
   - Check webhook processing
   - Verify hash calculation
   - Review payment polling logic

4. **Reservation not confirmed**
   - Check payment transaction status
   - Verify reservation update logic
   - Review database constraints

### Debug Mode
Enable debug logging by setting:
```env
LOG_LEVEL=debug
```

This will log all URWAY API requests and responses for troubleshooting.

## Support

For URWAY-specific issues, contact:
- Email: <EMAIL>
- Support: Futoon Alghadeer

For integration issues, refer to the code comments and error logs.
