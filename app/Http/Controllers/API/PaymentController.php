<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PaymentTransaction;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Services\UrwayPaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class PaymentController extends Controller
{
    private $urwayService;

    public function __construct()
    {
        $this->urwayService = new UrwayPaymentService();
    }

    /**
     * Initiate payment for a reservation
     */
    public function initiatePayment(Request $request)
    {
        try {
            $request->validate([
                'reservation_id' => 'required|integer|exists:service_category_item_reservations,id',
                'amount' => 'required|numeric|min:1',
                'customer_email' => 'nullable|email',
            ]);

            $user = Auth::user();
            $reservation = ServiceCategoryItemReservation::findOrFail($request->reservation_id);

            // Check if reservation belongs to user
            if ($reservation->user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to reservation',
                ], 403);
            }

            // Check if reservation is already paid
            if ($reservation->isPaid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Reservation is already paid',
                ], 400);
            }

            DB::beginTransaction();

            // Create payment transaction
            $paymentTransaction = PaymentTransaction::create([
                'transaction_id' => '', // Will be updated after URWAY response
                'payment_id' => PaymentTransaction::generatePaymentId(),
                'user_id' => $user->id,
                'reservation_id' => $reservation->id,
                'gateway_name' => 'Urway',
                'amount' => $request->amount,
                'currency' => 'SAR',
                'status' => 'pending',
                'metadata' => [
                    'reservation_from' => $reservation->reservation_from,
                    'reservation_to' => $reservation->reservation_to,
                    'property_title' => $reservation->serviceCategoryItem->title ?? '',
                ],
            ]);

            // Prepare payment data for URWAY
            $paymentData = [
                'payment_id' => $paymentTransaction->payment_id,
                'amount' => $request->amount,
                'customer_email' => $request->customer_email ?? $user->email ?? '',
                'reservation_id' => $reservation->id,
                'user_id' => $user->id,
            ];

            // Initiate payment with URWAY
            $urwayResponse = $this->urwayService->initiatePayment($paymentData);

            if ($urwayResponse['success']) {
                // Update payment transaction with URWAY response
                $paymentTransaction->update([
                    'transaction_id' => $urwayResponse['transaction_id'] ?? $urwayResponse['payment_id'],
                    'gateway_response' => $urwayResponse['response_data'],
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Payment initiated successfully',
                    'data' => [
                        'payment_id' => $paymentTransaction->payment_id,
                        'payment_url' => $urwayResponse['payment_url'],
                        'amount' => $paymentTransaction->amount,
                        'currency' => $paymentTransaction->currency,
                    ],
                ]);
            } else {
                DB::rollBack();
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to initiate payment',
                ], 500);
            }
        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Payment Initiation Failed', [
                'user_id' => Auth::id(),
                'reservation_id' => $request->reservation_id ?? null,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Payment initiation failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Request $request)
    {
        try {
            $request->validate([
                'payment_id' => 'required|string|exists:payment_transactions,payment_id',
            ]);

            $user = Auth::user();
            $paymentTransaction = PaymentTransaction::where('payment_id', $request->payment_id)
                ->where('user_id', $user->id)
                ->firstOrFail();

            // If payment is already completed, return cached status
            if ($paymentTransaction->isCompleted()) {
                return response()->json([
                    'success' => true,
                    'data' => [
                        'payment_id' => $paymentTransaction->payment_id,
                        'status' => $paymentTransaction->status,
                        'amount' => $paymentTransaction->amount,
                        'currency' => $paymentTransaction->currency,
                        'paid_at' => $paymentTransaction->paid_at,
                    ],
                ]);
            }

            // Verify payment status with URWAY
            $verificationResult = $this->urwayService->verifyPayment(
                $paymentTransaction->payment_id,
                $paymentTransaction->transaction_id
            );

            if ($verificationResult['success']) {
                // Update payment status based on verification
                $this->updatePaymentStatus($paymentTransaction, $verificationResult);

                return response()->json([
                    'success' => true,
                    'data' => [
                        'payment_id' => $paymentTransaction->payment_id,
                        'status' => $paymentTransaction->fresh()->status,
                        'amount' => $paymentTransaction->amount,
                        'currency' => $paymentTransaction->currency,
                        'paid_at' => $paymentTransaction->paid_at,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to verify payment status',
                ], 500);
            }
        } catch (Exception $e) {
            Log::error('Payment Status Check Failed', [
                'user_id' => Auth::id(),
                'payment_id' => $request->payment_id ?? null,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check payment status: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Handle URWAY callback/webhook
     */
    public function handleUrwayCallback(Request $request)
    {
        try {
            Log::info('URWAY Callback Received', $request->all());

            $callbackResult = $this->urwayService->handleCallback($request->all());

            if ($callbackResult['success']) {
                $paymentTransaction = PaymentTransaction::where('payment_id', $callbackResult['payment_id'])
                    ->first();

                if ($paymentTransaction) {
                    $this->updatePaymentStatus($paymentTransaction, $callbackResult);

                    // If payment is completed, confirm the reservation
                    if ($callbackResult['status'] === 'completed') {
                        $this->confirmReservation($paymentTransaction);
                    }

                    return response()->json(['status' => 'success']);
                } else {
                    Log::warning('Payment transaction not found for callback', [
                        'payment_id' => $callbackResult['payment_id']
                    ]);
                    return response()->json(['status' => 'payment_not_found'], 404);
                }
            } else {
                return response()->json(['status' => 'invalid_callback'], 400);
            }
        } catch (Exception $e) {
            Log::error('URWAY Callback Processing Failed', [
                'callback_data' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return response()->json(['status' => 'error'], 500);
        }
    }

    /**
     * Update payment status based on gateway response
     */
    private function updatePaymentStatus(PaymentTransaction $paymentTransaction, array $gatewayResult)
    {
        $status = $gatewayResult['status'];
        $gatewayResponse = $gatewayResult['response_data'] ?? [];

        if ($status === 'completed') {
            $paymentTransaction->markAsCompleted($gatewayResponse);
        } elseif (in_array($status, ['failed', 'cancelled'])) {
            $reason = $gatewayResponse['result'] ?? 'Payment failed';
            $paymentTransaction->markAsFailed($reason, $gatewayResponse);
        } else {
            $paymentTransaction->update([
                'status' => $status,
                'gateway_response' => array_merge($paymentTransaction->gateway_response ?? [], $gatewayResponse),
            ]);
        }

        // Update payment method if available
        if (isset($gatewayResult['payment_method'])) {
            $paymentTransaction->update(['payment_method' => $gatewayResult['payment_method']]);
        }
    }

    /**
     * Confirm reservation after successful payment
     */
    private function confirmReservation(PaymentTransaction $paymentTransaction)
    {
        if ($paymentTransaction->reservation) {
            $paymentTransaction->reservation->update(['confirmed' => 1]);
            
            Log::info('Reservation confirmed after payment', [
                'reservation_id' => $paymentTransaction->reservation_id,
                'payment_id' => $paymentTransaction->payment_id,
            ]);
        }
    }
}
