<?php
namespace App\Models\Admin;

use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemReservationDocument;
use App\Models\Admin\ServiceCategoryItemReservationField;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategoryItemReservation extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'reservation_date',
        'reservation_from',
        'reservation_to',
        'confirmed',
        'service_category_item_id',
        'service_category_form_id',
        'user_id',
    ];

    protected $casts = [
        'reservation_date' => 'date',     // Cast to Carbon date
        'reservation_from' => 'datetime', // Cast to Carbon datetime
        'reservation_to'   => 'datetime', // Cast to Carbon datetime
        'confirmed'        => 'boolean',
    ];

    public static function isPeriodAvailable($serviceCategoryItemId, $reservationFrom, $reservationTo)
    {
        return ! self::where('service_category_item_id', $serviceCategoryItemId)
            ->where(function ($query) use ($reservationFrom, $reservationTo) {
                $query->whereBetween('reservation_from', [$reservationFrom, $reservationTo])
                    ->orWhereBetween('reservation_to', [$reservationFrom, $reservationTo])
                    ->orWhere(function ($query) use ($reservationFrom, $reservationTo) {
                        $query->where('reservation_from', '<', $reservationFrom)
                            ->where('reservation_to', '>', $reservationTo);
                    });
            })
            ->exists();
    }

    public function serviceCategoryItem()
    {
        return $this->belongsTo(ServiceCategoryItem::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function documents()
    {
        return $this->hasMany(ServiceCategoryItemReservationDocument::class);
    }

    public function fields()
    {
        return $this->hasMany(ServiceCategoryItemReservationField::class);
    }

    public function paymentTransactions()
    {
        return $this->hasMany(\App\Models\PaymentTransaction::class, 'reservation_id');
    }

    public function latestPayment()
    {
        return $this->hasOne(\App\Models\PaymentTransaction::class, 'reservation_id')->latest();
    }

    public function completedPayment()
    {
        return $this->hasOne(\App\Models\PaymentTransaction::class, 'reservation_id')
            ->where('status', 'completed');
    }

    public function isPaid()
    {
        return $this->completedPayment()->exists();
    }
}
