import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:gather_point/feature/payments/data/services/payment_api_service.dart';
import 'package:gather_point/feature/payments/data/services/urway_payment_service.dart';
import 'package:gather_point/feature/payments/data/models/payment_transaction_model.dart';
import 'package:gather_point/feature/payments/presentation/views/payment_screen.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Generate mocks
@GenerateMocks([PaymentApiService, UrwayPaymentService])
import 'payment_flow_test.mocks.dart';

void main() {
  group('Payment Flow Integration Tests', () {
    late MockPaymentApiService mockPaymentApiService;
    late MockUrwayPaymentService mockUrwayPaymentService;

    setUp(() {
      mockPaymentApiService = MockPaymentApiService();
      mockUrwayPaymentService = MockUrwayPaymentService();
    });

    testWidgets('Payment screen displays correctly', (WidgetTester tester) async {
      // Arrange
      when(mockUrwayPaymentService.paymentApiService)
          .thenReturn(mockPaymentApiService);

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => PaymentCubit(mockUrwayPaymentService),
            child: const PaymentScreen(
              reservationId: 1,
              amount: 500.0,
              propertyTitle: 'Test Property',
              checkIn: DateTime(2025, 6, 25),
              checkOut: DateTime(2025, 6, 27),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Test Property'), findsOneWidget);
      expect(find.text('500.00'), findsAtLeastOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('Payment initiation shows loading state', (WidgetTester tester) async {
      // Arrange
      when(mockUrwayPaymentService.paymentApiService)
          .thenReturn(mockPaymentApiService);

      when(mockUrwayPaymentService.processPayment(
        context: anyNamed('context'),
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async {
        // Simulate delay
        await Future.delayed(const Duration(seconds: 2));
        return PaymentResult.success(
          paymentId: 'GP_TEST_123',
          amount: 500.0,
          currency: 'SAR',
        );
      });

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider(
            create: (context) => PaymentCubit(mockUrwayPaymentService),
            child: const PaymentScreen(
              reservationId: 1,
              amount: 500.0,
              propertyTitle: 'Test Property',
              checkIn: DateTime(2025, 6, 25),
              checkOut: DateTime(2025, 6, 27),
            ),
          ),
        ),
      );

      // Find and tap the pay button
      final payButton = find.byType(ElevatedButton);
      await tester.tap(payButton);
      await tester.pump();

      // Assert loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    test('PaymentApiService initiates payment correctly', () async {
      // Arrange
      final mockResponse = PaymentInitiationResponse(
        success: true,
        message: 'Payment initiated successfully',
        data: const PaymentInitiationData(
          paymentId: 'GP_TEST_123',
          paymentUrl: 'https://payments-dev.urway-tech.com/test',
          amount: 500.0,
          currency: 'SAR',
        ),
      );

      when(mockPaymentApiService.initiatePayment(
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async => mockResponse);

      // Act
      final result = await mockPaymentApiService.initiatePayment(
        reservationId: 1,
        amount: 500.0,
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(result.success, isTrue);
      expect(result.data?.paymentId, equals('GP_TEST_123'));
      expect(result.data?.amount, equals(500.0));
      expect(result.data?.currency, equals('SAR'));
    });

    test('PaymentApiService checks payment status correctly', () async {
      // Arrange
      final mockResponse = PaymentStatusResponse(
        success: true,
        message: 'Payment status retrieved',
        data: const PaymentStatusData(
          paymentId: 'GP_TEST_123',
          status: PaymentStatus.completed,
          amount: 500.0,
          currency: 'SAR',
          paidAt: null,
        ),
      );

      when(mockPaymentApiService.checkPaymentStatus(
        paymentId: anyNamed('paymentId'),
      )).thenAnswer((_) async => mockResponse);

      // Act
      final result = await mockPaymentApiService.checkPaymentStatus(
        paymentId: 'GP_TEST_123',
      );

      // Assert
      expect(result.success, isTrue);
      expect(result.data?.status, equals(PaymentStatus.completed));
      expect(result.data?.amount, equals(500.0));
    });

    test('UrwayPaymentService processes payment successfully', () async {
      // Arrange
      when(mockUrwayPaymentService.processPayment(
        context: anyNamed('context'),
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async => PaymentResult.success(
        paymentId: 'GP_TEST_123',
        amount: 500.0,
        currency: 'SAR',
      ));

      // Act
      final result = await mockUrwayPaymentService.processPayment(
        context: MockBuildContext(),
        reservationId: 1,
        amount: 500.0,
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(result.isSuccess, isTrue);
      expect(result.paymentId, equals('GP_TEST_123'));
      expect(result.amount, equals(500.0));
    });

    test('UrwayPaymentService handles payment failure', () async {
      // Arrange
      when(mockUrwayPaymentService.processPayment(
        context: anyNamed('context'),
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async => PaymentResult.failed('Payment declined'));

      // Act
      final result = await mockUrwayPaymentService.processPayment(
        context: MockBuildContext(),
        reservationId: 1,
        amount: 500.0,
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(result.isFailed, isTrue);
      expect(result.message, equals('Payment declined'));
    });

    test('PaymentCubit handles successful payment', () async {
      // Arrange
      final cubit = PaymentCubit(mockUrwayPaymentService);
      
      when(mockUrwayPaymentService.processPayment(
        context: anyNamed('context'),
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async => PaymentResult.success(
        paymentId: 'GP_TEST_123',
        amount: 500.0,
        currency: 'SAR',
      ));

      // Act
      await cubit.processPayment(
        context: MockBuildContext(),
        reservationId: 1,
        amount: 500.0,
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(cubit.state, isA<PaymentSuccess>());
      final successState = cubit.state as PaymentSuccess;
      expect(successState.paymentId, equals('GP_TEST_123'));
      expect(successState.amount, equals(500.0));
    });

    test('PaymentCubit handles payment failure', () async {
      // Arrange
      final cubit = PaymentCubit(mockUrwayPaymentService);
      
      when(mockUrwayPaymentService.processPayment(
        context: anyNamed('context'),
        reservationId: anyNamed('reservationId'),
        amount: anyNamed('amount'),
        customerEmail: anyNamed('customerEmail'),
      )).thenAnswer((_) async => PaymentResult.failed('Network error'));

      // Act
      await cubit.processPayment(
        context: MockBuildContext(),
        reservationId: 1,
        amount: 500.0,
        customerEmail: '<EMAIL>',
      );

      // Assert
      expect(cubit.state, isA<PaymentFailed>());
      final failedState = cubit.state as PaymentFailed;
      expect(failedState.message, equals('Network error'));
    });
  });
}

// Mock BuildContext for testing
class MockBuildContext extends Mock implements BuildContext {}
