<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\PaymentGatewaySetting;
use App\Models\PaymentTransaction;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Services\UrwayPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class PaymentIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $property;
    protected $reservation;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test property
        $this->property = ServiceCategoryItem::factory()->create([
            'title' => 'Test Property',
            'price' => 200.00,
            'active' => true,
        ]);
        
        // Create test reservation
        $this->reservation = ServiceCategoryItemReservation::create([
            'service_category_item_id' => $this->property->id,
            'user_id' => $this->user->id,
            'reservation_from' => now()->addDays(1),
            'reservation_to' => now()->addDays(3),
            'confirmed' => false,
        ]);
        
        // Set up URWAY configuration
        PaymentGatewaySetting::create([
            'gateway_name' => 'Urway',
            'api_key' => '75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef',
            'merchant_id' => 'gatherpoin',
            'terminal_id' => 'gatherpoin',
            'terminal_password' => 'gather@4512',
            'base_url' => 'https://payments-dev.urway-tech.com',
            'currency' => 'SAR',
            'callback_url' => 'http://localhost:8000/api/payments/urway/callback',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function it_can_initiate_payment_for_reservation()
    {
        $this->actingAs($this->user, 'api');

        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => $this->reservation->id,
            'amount' => 400.00,
            'customer_email' => $this->user->email,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'payment_id',
                        'payment_url',
                        'amount',
                        'currency',
                    ]
                ]);

        // Verify payment transaction was created
        $this->assertDatabaseHas('payment_transactions', [
            'user_id' => $this->user->id,
            'reservation_id' => $this->reservation->id,
            'amount' => 400.00,
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function it_prevents_payment_for_already_paid_reservation()
    {
        $this->actingAs($this->user, 'api');

        // Create a completed payment transaction
        PaymentTransaction::create([
            'transaction_id' => 'test_txn_123',
            'payment_id' => PaymentTransaction::generatePaymentId(),
            'user_id' => $this->user->id,
            'reservation_id' => $this->reservation->id,
            'gateway_name' => 'Urway',
            'amount' => 400.00,
            'currency' => 'SAR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => $this->reservation->id,
            'amount' => 400.00,
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Reservation is already paid',
                ]);
    }

    /** @test */
    public function it_can_check_payment_status()
    {
        $this->actingAs($this->user, 'api');

        $paymentTransaction = PaymentTransaction::create([
            'transaction_id' => 'test_txn_456',
            'payment_id' => PaymentTransaction::generatePaymentId(),
            'user_id' => $this->user->id,
            'reservation_id' => $this->reservation->id,
            'gateway_name' => 'Urway',
            'amount' => 400.00,
            'currency' => 'SAR',
            'status' => 'completed',
            'paid_at' => now(),
        ]);

        $response = $this->postJson('/api/payments/status', [
            'payment_id' => $paymentTransaction->payment_id,
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'payment_id',
                        'status',
                        'amount',
                        'currency',
                        'paid_at',
                    ]
                ]);
    }

    /** @test */
    public function it_handles_urway_callback_correctly()
    {
        $paymentTransaction = PaymentTransaction::create([
            'transaction_id' => 'test_txn_789',
            'payment_id' => 'GP_TEST_PAYMENT',
            'user_id' => $this->user->id,
            'reservation_id' => $this->reservation->id,
            'gateway_name' => 'Urway',
            'amount' => 400.00,
            'currency' => 'SAR',
            'status' => 'pending',
        ]);

        // Simulate URWAY callback data
        $callbackData = [
            'trackid' => 'GP_TEST_PAYMENT',
            'result' => 'CAPTURED',
            'amt' => '400.00',
            'currency' => 'SAR',
            'tranid' => 'urway_txn_123',
            'paymentMethod' => 'mada',
            'responseHash' => $this->generateTestHash('GP_TEST_PAYMENT', '400.00'),
        ];

        $response = $this->postJson('/api/payments/urway/callback', $callbackData);

        $response->assertStatus(200)
                ->assertJson(['status' => 'success']);

        // Verify payment transaction was updated
        $paymentTransaction->refresh();
        $this->assertEquals('completed', $paymentTransaction->status);
        $this->assertNotNull($paymentTransaction->paid_at);

        // Verify reservation was confirmed
        $this->reservation->refresh();
        $this->assertTrue($this->reservation->confirmed);
    }

    /** @test */
    public function it_validates_payment_initiation_request()
    {
        $this->actingAs($this->user, 'api');

        // Test missing reservation_id
        $response = $this->postJson('/api/payments/initiate', [
            'amount' => 400.00,
        ]);
        $response->assertStatus(422);

        // Test invalid amount
        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => $this->reservation->id,
            'amount' => -100,
        ]);
        $response->assertStatus(422);

        // Test non-existent reservation
        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => 99999,
            'amount' => 400.00,
        ]);
        $response->assertStatus(422);
    }

    /** @test */
    public function it_prevents_unauthorized_access_to_payment_endpoints()
    {
        // Test without authentication
        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => $this->reservation->id,
            'amount' => 400.00,
        ]);
        $response->assertStatus(401);

        // Test accessing another user's reservation
        $otherUser = User::factory()->create();
        $this->actingAs($otherUser, 'api');

        $response = $this->postJson('/api/payments/initiate', [
            'reservation_id' => $this->reservation->id,
            'amount' => 400.00,
        ]);
        $response->assertStatus(403);
    }

    /** @test */
    public function it_creates_host_earning_after_successful_payment()
    {
        $host = User::factory()->create();
        $this->property->update(['user_id' => $host->id]);

        $paymentTransaction = PaymentTransaction::create([
            'transaction_id' => 'test_txn_earning',
            'payment_id' => 'GP_TEST_EARNING',
            'user_id' => $this->user->id,
            'reservation_id' => $this->reservation->id,
            'gateway_name' => 'Urway',
            'amount' => 1000.00,
            'currency' => 'SAR',
            'status' => 'pending',
        ]);

        // Simulate successful payment callback
        $callbackData = [
            'trackid' => 'GP_TEST_EARNING',
            'result' => 'CAPTURED',
            'amt' => '1000.00',
            'currency' => 'SAR',
            'responseHash' => $this->generateTestHash('GP_TEST_EARNING', '1000.00'),
        ];

        $this->postJson('/api/payments/urway/callback', $callbackData);

        // Verify host earning was created (90% of payment after 10% commission)
        $this->assertDatabaseHas('wallet_transactions', [
            'user_id' => $host->id,
            'type' => 'earning',
            'amount' => 900.00, // 1000 - 10% commission
            'status' => 'completed',
            'reference_type' => 'reservation',
            'reference_id' => $this->reservation->id,
        ]);
    }

    /**
     * Generate test hash for URWAY callback simulation
     */
    private function generateTestHash($paymentId, $amount)
    {
        $config = PaymentGatewaySetting::where('gateway_name', 'Urway')->first();
        
        $hashString = $paymentId . '|' . 
                     $config->terminal_id . '|' . 
                     $config->terminal_password . '|' . 
                     $config->api_key . '|' . 
                     $amount . '|' . 
                     'SAR';

        return hash('sha256', $hashString);
    }
}
