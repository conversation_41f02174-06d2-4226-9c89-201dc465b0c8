<?php

namespace Database\Seeders;

use App\Models\PaymentGatewaySetting;
use Illuminate\Database\Seeder;

class UrwayPaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        PaymentGatewaySetting::updateOrCreate(
            ['gateway_name' => 'Urway'],
            [
                'api_key' => '75afc64e4e5f6f00bf1dd8e2c2ac7c17c6b3829fcc9b7aae991dc8f453a8dfef', // Merchant/Secret Key
                'merchant_id' => 'gatherpoin', // Terminal ID (used as merchant ID in URWAY)
                'terminal_id' => 'gatherpoin',
                'terminal_password' => 'gather@4512',
                'base_url' => 'https://payments-dev.urway-tech.com', // Test environment
                'currency' => 'SAR',
                'callback_url' => env('APP_URL') . '/api/payments/urway/callback',
                'is_active' => true,
            ]
        );
    }
}
