import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:gather_point/feature/reservations/data/services/reservations_api_service.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:intl/intl.dart';
import 'simple_booking_confirmation_screen.dart'; // صفحة تأكيد الحجز

class ReserveScreen extends StatefulWidget {
  final int serviceCategoryItemId; // Property ID for API calls
  final String placeTitle;
  final double pricePerNight;
  final String policy; // ✅ سياية الحجز
  final DateTime? checkInDate;
  final DateTime? checkOutDate;

  const ReserveScreen({
    super.key,
    required this.serviceCategoryItemId,
    required this.placeTitle,
    required this.pricePerNight,
    required this.policy,
    this.checkInDate,
    this.checkOutDate,
  });

  @override
  State<ReserveScreen> createState() => _ReserveScreenState();
}

class _ReserveScreenState extends State<ReserveScreen> {
  DateTime? checkInDate;
  DateTime? checkOutDate;
  int adults = 1;
  int children = 0;
  String paymentMethod = 'Visa';
  bool agreedToPolicies = false;
  bool isCreatingReservation = false; // Add loading state // ⬅️ جديد: الموافقة على السياسات

  final DateFormat formatter = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    // Initialize with passed dates if available
    checkInDate = widget.checkInDate;
    checkOutDate = widget.checkOutDate;
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    int numberOfNights = 0;
    double totalPrice = 0;

    if (checkInDate != null && checkOutDate != null) {
      numberOfNights = checkOutDate!.difference(checkInDate!).inDays;
      if (numberOfNights < 0) numberOfNights = 0;
      totalPrice = numberOfNights * widget.pricePerNight;
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(s.reserve, style: AppTextStyles.font18Bold.copyWith(color: Colors.white)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: ListView(
          children: [
            Text(
              widget.placeTitle,
              style: AppTextStyles.font24Bold.copyWith(color: Colors.white),
            ),
            const SizedBox(height: 20),

            // اختيار التواريخ
            buildDateTile(s.checkIn, checkInDate, true),
            buildDateTile(s.checkOut, checkOutDate, false),

            const SizedBox(height: 20),
// 🛡️ عرض سياسة الحجز
            Text(
              '${s.bookingPolicy}:',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              widget.policy,
              style: const TextStyle(fontSize: 16, color: Colors.white),
            ),
            const SizedBox(height: 20),
            // عدد الأشخاص
            buildCounterRow(
                'البالغين', adults, (val) => setState(() => adults = val)),
            buildCounterRow(
                'الأطفال', children, (val) => setState(() => children = val)),

            const SizedBox(height: 20),

            // طريقة الدفع
            buildPaymentDropdown(),

            const SizedBox(height: 20),

            // شروط الحجز ✅
            CheckboxListTile(
              title: const Text('أوافق على سياسات الحجز'),
              value: agreedToPolicies,
              onChanged: (value) {
                setState(() {
                  agreedToPolicies = value ?? false;
                });
              },
            ),
            const SizedBox(height: 10),
            if (!agreedToPolicies)
              const Text(
                'You must agree to the policies to confirm reservation.',
                style: TextStyle(color: Colors.red, fontSize: 14),
              ),

            const SizedBox(height: 20),

            // ملخص السعر
            if (numberOfNights > 0)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Number of nights: $numberOfNights',
                      style: const TextStyle(fontSize: 18)),
                  const SizedBox(height: 8),
                  Text('Total Price: €${totalPrice.toStringAsFixed(2)}',
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold)),
                ],
              ),

            const SizedBox(height: 30),

            // زر تأكيد الحجز
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.yellow[700],
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              onPressed: (checkInDate != null &&
                      checkOutDate != null &&
                      numberOfNights > 0 &&
                      agreedToPolicies &&
                      !isCreatingReservation)
                  ? () => _createReservationAndProceed(context, totalPrice)
                  : null,
              child: const Text(
                'تأكيد الحجز',
                style: TextStyle(color: Colors.black),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 🔵 Widgets مساعدة لترتيب الكود:
  Widget buildDateTile(String label, DateTime? date, bool isCheckIn) {
    return ListTile(
      leading: const Icon(Icons.calendar_today),
      title: Text(
        date == null
            ? 'Select $label date'
            : '$label: ${formatter.format(date)}',
      ),
      onTap: () => selectDate(isCheckIn: isCheckIn),
    );
  }

  Widget buildCounterRow(String label, int value, Function(int) onChanged) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: const TextStyle(fontSize: 18)),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.remove_circle_outline),
              onPressed: value > (label == 'Adults' ? 1 : 0)
                  ? () => onChanged(value - 1)
                  : null,
            ),
            Text('$value', style: const TextStyle(fontSize: 18)),
            IconButton(
              icon: const Icon(Icons.add_circle_outline),
              onPressed: () => onChanged(value + 1),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildPaymentDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Payment Method', style: TextStyle(fontSize: 18)),
        const SizedBox(height: 8),
        DropdownButton<String>(
          value: paymentMethod,
          isExpanded: true,
          items: ['Visa', 'PayPal', 'Cash']
              .map((method) => DropdownMenuItem(
                    value: method,
                    child: Text(method),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              paymentMethod = value!;
            });
          },
        ),
      ],
    );
  }

  Future<void> selectDate({required bool isCheckIn}) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isCheckIn) {
          checkInDate = picked;
          if (checkOutDate != null && checkOutDate!.isBefore(checkInDate!)) {
            checkOutDate = null;
          }
        } else {
          checkOutDate = picked;
        }
      });
    }
  }

  Future<void> _createReservationAndProceed(BuildContext context, double totalPrice) async {
    if (isCreatingReservation) return; // Prevent duplicate calls

    setState(() {
      isCreatingReservation = true;
    });

    try {
      print('🔄 Starting reservation creation...');

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Create reservation through API
      final reservationsService = ReservationsApiService(getIt<DioConsumer>());
      print('✅ ReservationsApiService created');

      // Add time component to dates (14:00 for check-in, 12:00 for check-out)
      final checkInDateTime = DateTime(
        checkInDate!.year,
        checkInDate!.month,
        checkInDate!.day,
        14, // 2 PM check-in
        0,
        0,
      );

      final checkOutDateTime = DateTime(
        checkOutDate!.year,
        checkOutDate!.month,
        checkOutDate!.day,
        12, // 12 PM check-out
        0,
        0,
      );

      // Use English locale for date formatting to avoid Arabic numerals
      final dateFormatter = DateFormat('yyyy-MM-dd HH:mm:ss', 'en_US');

      final fromDate = dateFormatter.format(checkInDateTime);
      final toDate = dateFormatter.format(checkOutDateTime);

      print('📅 Formatted dates:');
      print('   From: $fromDate');
      print('   To: $toDate');
      print('🏠 Property ID: ${widget.serviceCategoryItemId}');

      final reservationResult = await reservationsService.createReservation(
        serviceCategoryItemId: widget.serviceCategoryItemId,
        reservationFrom: fromDate,
        reservationTo: toDate,
        additionalData: {
          'adults': adults,
          'children': children,
          'payment_method': paymentMethod,
        },
      );

      print('✅ Reservation created successfully with ID: ${reservationResult.id}');

      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // Close loading dialog
      if (context.mounted) {
        print('🔄 Closing loading dialog...');
        Navigator.of(context).pop();
        print('✅ Loading dialog closed');
      }

      // Navigate to booking confirmation screen
      if (context.mounted) {
        print('🔄 Navigating to booking confirmation screen...');
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SimpleBookingConfirmationScreen(
              reservationId: reservationResult.id,
              placeTitle: widget.placeTitle,
              checkIn: checkInDate!,
              checkOut: checkOutDate!,
              adults: adults,
              children: children,
              totalPrice: totalPrice,
            ),
          ),
        );
        print('✅ Navigation completed');
      }
    } catch (e) {
      print('❌ Error creating reservation: $e');

      // Reset loading state
      setState(() {
        isCreatingReservation = false;
      });

      // Close loading dialog if still open
      if (context.mounted) {
        print('🔄 Closing loading dialog due to error...');
        Navigator.of(context).pop();
      }

      // Show error dialog
      if (context.mounted) {
        print('🔄 Showing error dialog...');
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('فشل في إنشاء الحجز: ${e.toString()}'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
      }
    }
  }
}
