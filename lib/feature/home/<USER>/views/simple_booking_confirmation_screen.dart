import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/payments/presentation/views/simple_payment_screen.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:gather_point/feature/payments/data/services/payment_api_service.dart';
import 'package:gather_point/feature/payments/data/services/urway_payment_service.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';

class SimpleBookingConfirmationScreen extends StatelessWidget {
  final int reservationId;
  final String placeTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final double totalPrice;
  final String? customerEmail;

  const SimpleBookingConfirmationScreen({
    super.key,
    required this.reservationId,
    required this.placeTitle,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.totalPrice,
    this.customerEmail,
  });

  @override
  Widget build(BuildContext context) {
    print('🔄 SimpleBookingConfirmationScreen build started');
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Booking Confirmation',
          style: AppTextStyles.font20Bold,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Simple booking summary
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Booking Summary',
                      style: AppTextStyles.font18Bold,
                    ),
                    const SizedBox(height: 16),
                    Text('Property: $placeTitle'),
                    const SizedBox(height: 8),
                    Text('Check In: ${checkIn.day}/${checkIn.month}/${checkIn.year}'),
                    const SizedBox(height: 8),
                    Text('Check Out: ${checkOut.day}/${checkOut.month}/${checkOut.year}'),
                    const SizedBox(height: 8),
                    Text('Guests: $adults adults, $children children'),
                    const SizedBox(height: 8),
                    Text('Nights: ${checkOut.difference(checkIn).inDays}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Simple pricing
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Price Breakdown',
                      style: AppTextStyles.font18Bold,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Total Amount'),
                        Text(
                          '${totalPrice.toStringAsFixed(2)} SAR',
                          style: AppTextStyles.font18Bold.copyWith(
                            color: AppColors.yellow,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const Spacer(),
            
            // Payment button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () => _proceedToPayment(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                ),
                child: const Text(
                  'Proceed to Payment',
                  style: AppTextStyles.font18Bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _proceedToPayment(BuildContext context) {
    print('🔄 Proceeding to payment...');
    
    try {
      // Create payment services
      final dioConsumer = getIt<DioConsumer>();
      final paymentApiService = PaymentApiService(dioConsumer);
      final urwayPaymentService = UrwayPaymentService(paymentApiService);

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => BlocProvider(
            create: (context) => PaymentCubit(urwayPaymentService),
            child: SimplePaymentScreen(
              reservationId: reservationId,
              amount: totalPrice,
              propertyTitle: placeTitle,
              checkIn: checkIn,
              checkOut: checkOut,
              customerEmail: customerEmail,
            ),
          ),
        ),
      );
      
      print('✅ Navigation to payment screen completed');
    } catch (e) {
      print('❌ Error proceeding to payment: $e');
      
      // Show error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text('Failed to proceed to payment: ${e.toString()}'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
