import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/payments/presentation/views/simple_payment_screen.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:gather_point/feature/payments/data/services/payment_api_service.dart';
import 'package:gather_point/feature/payments/data/services/urway_payment_service.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:intl/intl.dart';

class BookingConfirmationScreen extends StatelessWidget {
  final int reservationId;
  final String placeTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final double totalPrice;
  final String? customerEmail;

  const BookingConfirmationScreen({
    super.key,
    required this.reservationId,
    required this.placeTitle,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.totalPrice,
    this.customerEmail,
  });

  @override
  Widget build(BuildContext context) {
    // Use English locale for NumberFormat to avoid Arabic locale issues
    final formatter = NumberFormat("#,##0.00", "en_US");

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Booking Confirmation',
          style: AppTextStyles.font20Bold,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBookingSummary(context, formatter),
            const SizedBox(height: 24),
            _buildPricingBreakdown(context, formatter),
            const SizedBox(height: 24),
            _buildCancellationPolicy(context),
            const SizedBox(height: 32),
            _buildConfirmButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingSummary(BuildContext context, NumberFormat formatter) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Summary', // s.bookingSummary,
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Property', placeTitle), // s.property
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Check In', // s.checkIn,
              '${checkIn.day}/${checkIn.month}/${checkIn.year}',
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Check Out', // s.checkOut,
              '${checkOut.day}/${checkOut.month}/${checkOut.year}',
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Guests', // s.guests,
              'Adults: $adults, Children: $children',
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Number of Nights', // s.numberOfNights,
              '${checkOut.difference(checkIn).inDays}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.font16Regular,
        ),
        Text(
          value,
          style: AppTextStyles.font16SemiBold,
        ),
      ],
    );
  }

  Widget _buildPricingBreakdown(BuildContext context, NumberFormat formatter) {
    final nights = checkOut.difference(checkIn).inDays;
    final pricePerNight = totalPrice / nights;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Price Breakdown',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            _buildPriceRow(
              'Price per night × $nights nights',
              '${formatter.format(totalPrice)} SAR',
            ),
            const Divider(height: 24),
            _buildPriceRow(
              'Total Amount',
              '${formatter.format(totalPrice)} SAR',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal
              ? AppTextStyles.font18Bold
              : AppTextStyles.font16Regular,
        ),
        Text(
          value,
          style: isTotal
              ? AppTextStyles.font18Bold.copyWith(
                  color: AppColors.yellow,
                )
              : AppTextStyles.font16Regular,
        ),
      ],
    );
  }

  Widget _buildCancellationPolicy(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Cancellation Policy',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Free cancellation up to 24 hours before check-in. After that, cancellation fees may apply.',
              style: AppTextStyles.font14Regular.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () => _proceedToPayment(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.yellow,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: const Text(
          'Proceed to Payment',
          style: AppTextStyles.font18Bold,
        ),
      ),
    );
  }

  void _proceedToPayment(BuildContext context) {
    print('🔄 _proceedToPayment method called');

    try {
      // Create payment services
      final dioConsumer = getIt<DioConsumer>();
      print('✅ DioConsumer retrieved from service locator');

      final paymentApiService = PaymentApiService(dioConsumer);
      print('✅ PaymentApiService created');

      final urwayPaymentService = UrwayPaymentService(paymentApiService);
      print('✅ UrwayPaymentService created');

      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => BlocProvider(
            create: (context) {
              final cubit = PaymentCubit(urwayPaymentService);
              // Ensure cubit starts in initial state
              cubit.resetPayment();
              return cubit;
            },
            child: SimplePaymentScreen(
              reservationId: reservationId,
              amount: totalPrice,
              propertyTitle: placeTitle,
              checkIn: checkIn,
              checkOut: checkOut,
              customerEmail: customerEmail,
            ),
          ),
        ),
      );

      print('✅ Navigation to payment screen initiated');
    } catch (e) {
      print('❌ Error in _proceedToPayment: $e');

      // Show error dialog
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Error'),
          content: Text('Failed to proceed to payment: ${e.toString()}'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }
}
