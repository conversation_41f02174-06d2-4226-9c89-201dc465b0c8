import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/payments/presentation/views/payment_screen.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:gather_point/feature/payments/data/services/payment_api_service.dart';
import 'package:gather_point/feature/payments/data/services/urway_payment_service.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';

class BookingConfirmationScreen extends StatelessWidget {
  final int reservationId;
  final String placeTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int adults;
  final int children;
  final double totalPrice;
  final String? customerEmail;

  const BookingConfirmationScreen({
    super.key,
    required this.reservationId,
    required this.placeTitle,
    required this.checkIn,
    required this.checkOut,
    required this.adults,
    required this.children,
    required this.totalPrice,
    this.customerEmail,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final formatter = NumberFormat("#,##0.00", "ar");
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          s.bookingConfirmation,
          style: AppTextStyles.headlineMedium,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBookingSummary(context, s, formatter),
            const SizedBox(height: 24),
            _buildPricingBreakdown(context, s, formatter),
            const SizedBox(height: 24),
            _buildCancellationPolicy(context, s),
            const SizedBox(height: 32),
            _buildConfirmButton(context, s),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingSummary(BuildContext context, S s, NumberFormat formatter) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              s.bookingSummary,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryRow(s.property, placeTitle),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.checkIn,
              DateFormat('dd/MM/yyyy').format(checkIn),
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.checkOut,
              DateFormat('dd/MM/yyyy').format(checkOut),
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.guests,
              '${s.adults}: $adults, ${s.children}: $children',
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.numberOfNights,
              '${checkOut.difference(checkIn).inDays}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium,
        ),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildPricingBreakdown(BuildContext context, S s, NumberFormat formatter) {
    final nights = checkOut.difference(checkIn).inDays;
    final pricePerNight = totalPrice / nights;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              s.priceBreakdown,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPriceRow(
              '${s.pricePerNight} × $nights ${s.nights}',
              s.priceWithCurrency(formatter.format(totalPrice)),
            ),
            const Divider(height: 24),
            _buildPriceRow(
              s.totalAmount,
              s.priceWithCurrency(formatter.format(totalPrice)),
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal 
              ? AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.bold)
              : AppTextStyles.bodyMedium,
        ),
        Text(
          value,
          style: isTotal 
              ? AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.yellow,
                )
              : AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildCancellationPolicy(BuildContext context, S s) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade600,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  s.cancellationPolicy,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              s.cancellationPolicyDetails,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmButton(BuildContext context, S s) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () => _proceedToPayment(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.yellow,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: Text(
          s.proceedToPayment,
          style: AppTextStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  void _proceedToPayment(BuildContext context) {
    // Create payment services
    final dioConsumer = getIt<DioConsumer>();
    final paymentApiService = PaymentApiService(dioConsumer);
    final urwayPaymentService = UrwayPaymentService(paymentApiService);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => PaymentCubit(urwayPaymentService),
          child: PaymentScreen(
            reservationId: reservationId,
            amount: totalPrice,
            propertyTitle: placeTitle,
            checkIn: checkIn,
            checkOut: checkOut,
            customerEmail: customerEmail,
          ),
        ),
      ),
    );
  }
}
