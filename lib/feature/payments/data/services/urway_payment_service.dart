import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:gather_point/feature/payments/data/models/payment_transaction_model.dart';
import 'package:gather_point/feature/payments/data/services/payment_api_service.dart';

class UrwayPaymentService {
  final PaymentApiService _paymentApiService;

  UrwayPaymentService(this._paymentApiService);

  PaymentApiService get paymentApiService => _paymentApiService;

  /// Process payment through URWAY
  Future<PaymentResult> processPayment({
    required BuildContext context,
    required int reservationId,
    required double amount,
    String? customerEmail,
  }) async {
    try {
      // Step 1: Initiate payment with backend
      final initiationResponse = await _paymentApiService.initiatePayment(
        reservationId: reservationId,
        amount: amount,
        customerEmail: customerEmail,
      );

      if (!initiationResponse.success || initiationResponse.data == null) {
        return PaymentResult.failed(initiationResponse.message);
      }

      final paymentData = initiationResponse.data!;

      // Step 2: Open URWAY payment page in webview
      final webviewResult = await _openPaymentWebview(
        context: context,
        paymentUrl: paymentData.paymentUrl,
        paymentId: paymentData.paymentId,
      );

      if (webviewResult.isSuccess) {
        // Step 3: Verify payment status with backend
        final statusResponse = await _paymentApiService.checkPaymentStatus(
          paymentId: paymentData.paymentId,
        );

        if (statusResponse.success && statusResponse.data != null) {
          final paymentStatus = statusResponse.data!.status;
          
          if (paymentStatus == PaymentStatus.completed) {
            return PaymentResult.success(
              paymentId: paymentData.paymentId,
              amount: paymentData.amount,
              currency: paymentData.currency,
            );
          } else if (paymentStatus == PaymentStatus.failed || 
                     paymentStatus == PaymentStatus.cancelled) {
            return PaymentResult.failed('Payment was not completed');
          } else {
            return PaymentResult.pending(
              paymentId: paymentData.paymentId,
              message: 'Payment is being processed',
            );
          }
        } else {
          return PaymentResult.failed('Failed to verify payment status');
        }
      } else {
        return PaymentResult.cancelled('Payment was cancelled by user');
      }
    } catch (e) {
      log('Payment processing error: $e');
      return PaymentResult.failed('Payment processing failed: ${e.toString()}');
    }
  }

  /// Open URWAY payment page in webview
  Future<WebviewResult> _openPaymentWebview({
    required BuildContext context,
    required String paymentUrl,
    required String paymentId,
  }) async {
    final completer = Completer<WebviewResult>();

    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => UrwayPaymentWebview(
          paymentUrl: paymentUrl,
          paymentId: paymentId,
          onResult: (result) {
            if (!completer.isCompleted) {
              completer.complete(result);
            }
          },
        ),
      ),
    );

    // If the completer hasn't completed yet, it means the user closed the webview
    if (!completer.isCompleted) {
      completer.complete(WebviewResult.cancelled());
    }

    return completer.future;
  }

  /// Poll payment status until completion or timeout
  Future<PaymentStatus> pollPaymentStatus({
    required String paymentId,
    Duration timeout = const Duration(minutes: 5),
    Duration interval = const Duration(seconds: 3),
  }) async {
    final endTime = DateTime.now().add(timeout);

    while (DateTime.now().isBefore(endTime)) {
      try {
        final response = await _paymentApiService.checkPaymentStatus(
          paymentId: paymentId,
        );

        if (response.success && response.data != null) {
          final status = response.data!.status;
          
          // Return if payment is in a final state
          if (status == PaymentStatus.completed ||
              status == PaymentStatus.failed ||
              status == PaymentStatus.cancelled) {
            return status;
          }
        }

        // Wait before next poll
        await Future.delayed(interval);
      } catch (e) {
        log('Error polling payment status: $e');
        await Future.delayed(interval);
      }
    }

    // Timeout reached
    return PaymentStatus.pending;
  }
}

class UrwayPaymentWebview extends StatefulWidget {
  final String paymentUrl;
  final String paymentId;
  final Function(WebviewResult) onResult;

  const UrwayPaymentWebview({
    super.key,
    required this.paymentUrl,
    required this.paymentId,
    required this.onResult,
  });

  @override
  State<UrwayPaymentWebview> createState() => _UrwayPaymentWebviewState();
}

class _UrwayPaymentWebviewState extends State<UrwayPaymentWebview> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeWebview();
  }

  void _initializeWebview() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
            _checkForPaymentCompletion(url);
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _hasError = true;
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            _checkForPaymentCompletion(request.url);
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  void _checkForPaymentCompletion(String url) {
    // Check if URL indicates payment completion or failure
    if (url.contains('success') || url.contains('completed')) {
      widget.onResult(WebviewResult.success());
      Navigator.of(context).pop();
    } else if (url.contains('failed') || url.contains('error')) {
      widget.onResult(WebviewResult.failed());
      Navigator.of(context).pop();
    } else if (url.contains('cancel')) {
      widget.onResult(WebviewResult.cancelled());
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            widget.onResult(WebviewResult.cancelled());
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Stack(
        children: [
          if (!_hasError)
            WebViewWidget(controller: _controller)
          else
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text('Failed to load payment page'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _hasError = false;
                      });
                      _controller.loadRequest(Uri.parse(widget.paymentUrl));
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}

class PaymentResult {
  final PaymentResultType type;
  final String message;
  final String? paymentId;
  final double? amount;
  final String? currency;

  const PaymentResult._({
    required this.type,
    required this.message,
    this.paymentId,
    this.amount,
    this.currency,
  });

  factory PaymentResult.success({
    required String paymentId,
    required double amount,
    required String currency,
  }) {
    return PaymentResult._(
      type: PaymentResultType.success,
      message: 'Payment completed successfully',
      paymentId: paymentId,
      amount: amount,
      currency: currency,
    );
  }

  factory PaymentResult.failed(String message) {
    return PaymentResult._(
      type: PaymentResultType.failed,
      message: message,
    );
  }

  factory PaymentResult.cancelled(String message) {
    return PaymentResult._(
      type: PaymentResultType.cancelled,
      message: message,
    );
  }

  factory PaymentResult.pending({
    required String paymentId,
    required String message,
  }) {
    return PaymentResult._(
      type: PaymentResultType.pending,
      message: message,
      paymentId: paymentId,
    );
  }

  bool get isSuccess => type == PaymentResultType.success;
  bool get isFailed => type == PaymentResultType.failed;
  bool get isCancelled => type == PaymentResultType.cancelled;
  bool get isPending => type == PaymentResultType.pending;
}

enum PaymentResultType { success, failed, cancelled, pending }

class WebviewResult {
  final WebviewResultType type;

  const WebviewResult._(this.type);

  factory WebviewResult.success() => const WebviewResult._(WebviewResultType.success);
  factory WebviewResult.failed() => const WebviewResult._(WebviewResultType.failed);
  factory WebviewResult.cancelled() => const WebviewResult._(WebviewResultType.cancelled);

  bool get isSuccess => type == WebviewResultType.success;
  bool get isFailed => type == WebviewResultType.failed;
  bool get isCancelled => type == WebviewResultType.cancelled;
}

enum WebviewResultType { success, failed, cancelled }
