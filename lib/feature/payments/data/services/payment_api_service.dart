import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/payments/data/models/payment_transaction_model.dart';

class PaymentApiService {
  final DioConsumer _dioConsumer;

  PaymentApiService(this._dioConsumer);

  /// Initiate payment for a reservation
  Future<PaymentInitiationResponse> initiatePayment({
    required int reservationId,
    required double amount,
    String? customerEmail,
  }) async {
    try {
      final data = {
        'reservation_id': reservationId,
        'amount': amount,
        if (customerEmail != null) 'customer_email': customerEmail,
      };

      final response = await _dioConsumer.post(
        EndPoints.paymentInitiate,
        data: data,
      );

      return PaymentInitiationResponse.fromJson(response);
    } catch (e) {
      throw Exception('Failed to initiate payment: ${e.toString()}');
    }
  }

  /// Check payment status
  Future<PaymentStatusResponse> checkPaymentStatus({
    required String paymentId,
  }) async {
    try {
      final data = {
        'payment_id': paymentId,
      };

      final response = await _dioConsumer.post(
        EndPoints.paymentStatus,
        data: data,
      );

      return PaymentStatusResponse.fromJson(response);
    } catch (e) {
      throw Exception('Failed to check payment status: ${e.toString()}');
    }
  }

  /// Get payment transaction details
  Future<PaymentTransactionModel> getPaymentTransaction({
    required String paymentId,
  }) async {
    try {
      final response = await checkPaymentStatus(paymentId: paymentId);
      
      if (response.success && response.data != null) {
        // Convert PaymentStatusData to PaymentTransactionModel
        final data = response.data!;
        return PaymentTransactionModel(
          paymentId: data.paymentId,
          transactionId: '', // Not provided in status response
          userId: 0, // Not provided in status response
          gatewayName: 'Urway',
          amount: data.amount,
          currency: data.currency,
          status: data.status,
          paidAt: data.paidAt,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      throw Exception('Failed to get payment transaction: ${e.toString()}');
    }
  }
}
