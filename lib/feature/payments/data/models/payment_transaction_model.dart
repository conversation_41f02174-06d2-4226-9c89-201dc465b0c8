class PaymentTransactionModel {
  final String paymentId;
  final String transactionId;
  final int userId;
  final int? reservationId;
  final String gatewayName;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final PaymentMethod? paymentMethod;
  final Map<String, dynamic>? gatewayResponse;
  final Map<String, dynamic>? metadata;
  final DateTime? paidAt;
  final DateTime? failedAt;
  final String? failureReason;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PaymentTransactionModel({
    required this.paymentId,
    required this.transactionId,
    required this.userId,
    this.reservationId,
    required this.gatewayName,
    required this.amount,
    required this.currency,
    required this.status,
    this.paymentMethod,
    this.gatewayResponse,
    this.metadata,
    this.paidAt,
    this.failedAt,
    this.failureReason,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentTransactionModel.fromJson(Map<String, dynamic> json) {
    return PaymentTransactionModel(
      paymentId: json['payment_id'] ?? '',
      transactionId: json['transaction_id'] ?? '',
      userId: json['user_id'] ?? 0,
      reservationId: json['reservation_id'],
      gatewayName: json['gateway_name'] ?? 'Urway',
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'SAR',
      status: PaymentStatus.fromString(json['status'] ?? 'pending'),
      paymentMethod: json['payment_method'] != null 
          ? PaymentMethod.fromString(json['payment_method'])
          : null,
      gatewayResponse: json['gateway_response'],
      metadata: json['metadata'],
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
      failedAt: json['failed_at'] != null ? DateTime.parse(json['failed_at']) : null,
      failureReason: json['failure_reason'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'payment_id': paymentId,
      'transaction_id': transactionId,
      'user_id': userId,
      'reservation_id': reservationId,
      'gateway_name': gatewayName,
      'amount': amount,
      'currency': currency,
      'status': status.value,
      'payment_method': paymentMethod?.value,
      'gateway_response': gatewayResponse,
      'metadata': metadata,
      'paid_at': paidAt?.toIso8601String(),
      'failed_at': failedAt?.toIso8601String(),
      'failure_reason': failureReason,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isCompleted => status == PaymentStatus.completed;
  bool get isPending => status == PaymentStatus.pending;
  bool get isFailed => status == PaymentStatus.failed || status == PaymentStatus.cancelled;
}

enum PaymentStatus {
  pending('pending'),
  processing('processing'),
  completed('completed'),
  failed('failed'),
  cancelled('cancelled'),
  refunded('refunded');

  const PaymentStatus(this.value);
  final String value;

  static PaymentStatus fromString(String value) {
    return PaymentStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => PaymentStatus.pending,
    );
  }
}

enum PaymentMethod {
  creditCard('credit_card'),
  debitCard('debit_card'),
  mada('mada'),
  applePay('apple_pay'),
  stcPay('stc_pay'),
  other('other');

  const PaymentMethod(this.value);
  final String value;

  static PaymentMethod fromString(String value) {
    return PaymentMethod.values.firstWhere(
      (method) => method.value == value,
      orElse: () => PaymentMethod.other,
    );
  }

  String get displayName {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.mada:
        return 'MADA';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.stcPay:
        return 'STC Pay';
      case PaymentMethod.other:
        return 'Other';
    }
  }
}

class PaymentInitiationResponse {
  final bool success;
  final String message;
  final PaymentInitiationData? data;

  const PaymentInitiationResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentInitiationResponse.fromJson(Map<String, dynamic> json) {
    return PaymentInitiationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? PaymentInitiationData.fromJson(json['data'])
          : null,
    );
  }
}

class PaymentInitiationData {
  final String paymentId;
  final String paymentUrl;
  final double amount;
  final String currency;

  const PaymentInitiationData({
    required this.paymentId,
    required this.paymentUrl,
    required this.amount,
    required this.currency,
  });

  factory PaymentInitiationData.fromJson(Map<String, dynamic> json) {
    return PaymentInitiationData(
      paymentId: json['payment_id'] ?? '',
      paymentUrl: json['payment_url'] ?? '',
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'SAR',
    );
  }
}

class PaymentStatusResponse {
  final bool success;
  final String message;
  final PaymentStatusData? data;

  const PaymentStatusResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory PaymentStatusResponse.fromJson(Map<String, dynamic> json) {
    return PaymentStatusResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null 
          ? PaymentStatusData.fromJson(json['data'])
          : null,
    );
  }
}

class PaymentStatusData {
  final String paymentId;
  final PaymentStatus status;
  final double amount;
  final String currency;
  final DateTime? paidAt;

  const PaymentStatusData({
    required this.paymentId,
    required this.status,
    required this.amount,
    required this.currency,
    this.paidAt,
  });

  factory PaymentStatusData.fromJson(Map<String, dynamic> json) {
    return PaymentStatusData(
      paymentId: json['payment_id'] ?? '',
      status: PaymentStatus.fromString(json['status'] ?? 'pending'),
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      currency: json['currency'] ?? 'SAR',
      paidAt: json['paid_at'] != null ? DateTime.parse(json['paid_at']) : null,
    );
  }
}
