import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:gather_point/feature/payments/presentation/views/payment_success_screen.dart';
import 'package:gather_point/feature/payments/presentation/views/payment_failure_screen.dart';
import 'package:gather_point/feature/payments/data/models/payment_transaction_model.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';

class PaymentScreen extends StatefulWidget {
  final int reservationId;
  final double amount;
  final String propertyTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final String? customerEmail;

  const PaymentScreen({
    super.key,
    required this.reservationId,
    required this.amount,
    required this.propertyTitle,
    required this.checkIn,
    required this.checkOut,
    this.customerEmail,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          s.payment,
          style: AppTextStyles.headlineMedium,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: BlocConsumer<PaymentCubit, PaymentState>(
        listener: (context, state) {
          if (state is PaymentSuccess) {
            _navigateToPaymentSuccess(context, state);
          } else if (state is PaymentFailed) {
            _navigateToPaymentFailure(context, state.message);
          } else if (state is PaymentCancelled) {
            _showPaymentCancelledDialog(context);
          }
        },
        builder: (context, state) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBookingSummary(context),
                const SizedBox(height: 24),
                _buildPaymentMethods(context),
                const SizedBox(height: 24),
                _buildSecurityInfo(context),
                const SizedBox(height: 32),
                _buildPayButton(context, state),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBookingSummary(BuildContext context) {
    final s = S.of(context);
    final formatter = NumberFormat("#,##0.00", "ar");
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              s.bookingSummary,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSummaryRow(s.property, widget.propertyTitle),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.checkIn,
              DateFormat('dd/MM/yyyy').format(widget.checkIn),
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              s.checkOut,
              DateFormat('dd/MM/yyyy').format(widget.checkOut),
            ),
            const Divider(height: 24),
            _buildSummaryRow(
              s.totalAmount,
              s.priceWithCurrency(formatter.format(widget.amount)),
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal 
              ? AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.bold)
              : AppTextStyles.bodyMedium,
        ),
        Text(
          value,
          style: isTotal 
              ? AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.yellow,
                )
              : AppTextStyles.bodyMedium,
        ),
      ],
    );
  }

  Widget _buildPaymentMethods(BuildContext context) {
    final s = S.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              s.paymentMethods,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildPaymentMethodsGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsGrid() {
    // MADA logo should be positioned first as per URWAY requirements
    final paymentMethods = [
      {'name': 'MADA', 'icon': 'assets/images/mada_logo.png', 'isFirst': true},
      {'name': 'Visa', 'icon': 'assets/images/visa_logo.png', 'isFirst': false},
      {'name': 'Mastercard', 'icon': 'assets/images/mastercard_logo.png', 'isFirst': false},
      {'name': 'Apple Pay', 'icon': 'assets/images/apple_pay_logo.png', 'isFirst': false},
      {'name': 'STC Pay', 'icon': 'assets/images/stc_pay_logo.png', 'isFirst': false},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: paymentMethods.map((method) {
        return Container(
          width: (MediaQuery.of(context).size.width - 80) / 3,
          height: 60,
          decoration: BoxDecoration(
            border: Border.all(
              color: method['isFirst'] as bool 
                  ? AppColors.yellow 
                  : Colors.grey.shade300,
              width: method['isFirst'] as bool ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Image.asset(
              method['icon'] as String,
              height: 30,
              errorBuilder: (context, error, stackTrace) {
                return Text(
                  method['name'] as String,
                  style: AppTextStyles.bodySmall,
                  textAlign: TextAlign.center,
                );
              },
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSecurityInfo(BuildContext context) {
    final s = S.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.green.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  s.securePayment,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  s.paymentSecurityMessage,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayButton(BuildContext context, PaymentState state) {
    final s = S.of(context);
    final isLoading = state is PaymentLoading;
    
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : () => _processPayment(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.yellow,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : Text(
                s.payNow,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  void _processPayment(BuildContext context) {
    context.read<PaymentCubit>().processPayment(
      context: context,
      reservationId: widget.reservationId,
      amount: widget.amount,
      customerEmail: widget.customerEmail,
    );
  }

  void _navigateToPaymentSuccess(BuildContext context, PaymentSuccess state) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PaymentSuccessScreen(
          paymentId: state.paymentId,
          amount: state.amount,
          currency: state.currency,
          propertyTitle: widget.propertyTitle,
          checkIn: widget.checkIn,
          checkOut: widget.checkOut,
          reservationId: widget.reservationId,
        ),
      ),
    );
  }

  void _navigateToPaymentFailure(BuildContext context, String message) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PaymentFailureScreen(
          errorMessage: message,
          onRetry: () {
            Navigator.of(context).pop(); // Go back to payment screen
            _processPayment(context);
          },
          canRetry: true,
        ),
      ),
    );
  }

  void _showPaymentCancelledDialog(BuildContext context) {
    final s = S.of(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.cancel,
              color: Colors.orange,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              s.paymentCancelled,
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              s.paymentCancelledMessage,
              style: AppTextStyles.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(s.ok),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
