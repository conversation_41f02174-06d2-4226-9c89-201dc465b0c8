import 'package:flutter/material.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/profile/presentation/views/my_bookings_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:intl/intl.dart';

class PaymentSuccessScreen extends StatelessWidget {
  final String paymentId;
  final double amount;
  final String currency;
  final String propertyTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final int reservationId;

  const PaymentSuccessScreen({
    super.key,
    required this.paymentId,
    required this.amount,
    required this.currency,
    required this.propertyTitle,
    required this.checkIn,
    required this.checkOut,
    required this.reservationId,
  });

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final formatter = NumberFormat("#,##0.00", "ar");
    
    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const Spacer(),
              
              // Success Icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.check_circle,
                  color: Colors.green.shade600,
                  size: 80,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Success Title
              Text(
                s.paymentSuccessful,
                style: AppTextStyles.headlineLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              // Success Message
              Text(
                s.reservationConfirmed,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              // Payment Details Card
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      _buildDetailRow(
                        s.paymentId,
                        paymentId,
                        Icons.receipt_long,
                      ),
                      const SizedBox(height: 16),
                      _buildDetailRow(
                        s.property,
                        propertyTitle,
                        Icons.location_on,
                      ),
                      const SizedBox(height: 16),
                      _buildDetailRow(
                        s.checkIn,
                        DateFormat('dd/MM/yyyy').format(checkIn),
                        Icons.calendar_today,
                      ),
                      const SizedBox(height: 16),
                      _buildDetailRow(
                        s.checkOut,
                        DateFormat('dd/MM/yyyy').format(checkOut),
                        Icons.calendar_today,
                      ),
                      const Divider(height: 32),
                      _buildDetailRow(
                        s.totalPaid,
                        s.priceWithCurrency(formatter.format(amount)),
                        Icons.payment,
                        isHighlighted: true,
                      ),
                    ],
                  ),
                ),
              ),
              
              const Spacer(),
              
              // Action Buttons
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton.icon(
                      onPressed: () => _navigateToMyBookings(context),
                      icon: const Icon(Icons.list_alt),
                      label: Text(s.viewMyBookings),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.yellow,
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: OutlinedButton.icon(
                      onPressed: () => _navigateToHome(context),
                      icon: const Icon(Icons.home),
                      label: Text(s.backToHome),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.black,
                        side: const BorderSide(color: Colors.grey),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(28),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon, {
    bool isHighlighted = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: isHighlighted ? AppColors.yellow : Colors.grey.shade600,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall.copyWith(
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: isHighlighted
                    ? AppTextStyles.bodyLarge.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.yellow,
                      )
                    : AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToMyBookings(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const MyBookingsScreen(),
      ),
      (route) => route.isFirst,
    );
  }

  void _navigateToHome(BuildContext context) {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }
}
