import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/styles/app_colors.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/feature/payments/presentation/cubit/payment_cubit.dart';
import 'package:intl/intl.dart';

class SimplePaymentScreen extends StatefulWidget {
  final int reservationId;
  final double amount;
  final String propertyTitle;
  final DateTime checkIn;
  final DateTime checkOut;
  final String? customerEmail;

  const SimplePaymentScreen({
    super.key,
    required this.reservationId,
    required this.amount,
    required this.propertyTitle,
    required this.checkIn,
    required this.checkOut,
    this.customerEmail,
  });

  @override
  State<SimplePaymentScreen> createState() => _SimplePaymentScreenState();
}

class _SimplePaymentScreenState extends State<SimplePaymentScreen> {
  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat("#,##0.00", "ar");

    print('🔄 SimplePaymentScreen build - PaymentCubit state: ${context.read<PaymentCubit>().state}');

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Payment',
          style: AppTextStyles.font20Bold,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: BlocConsumer<PaymentCubit, PaymentState>(
          listener: (context, state) {
            if (state is PaymentSuccess) {
              _showSuccessDialog(context);
            } else if (state is PaymentFailed) {
              _showErrorDialog(context, state.message);
            } else if (state is PaymentCancelled) {
              _showCancelledDialog(context);
            }
          },
          builder: (context, state) {
            print('🔄 BlocConsumer builder - PaymentState: $state');

            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildBookingSummary(formatter),
                  const SizedBox(height: 24),
                  _buildPaymentMethods(),
                  const SizedBox(height: 24),
                  _buildSecurityInfo(),
                  const SizedBox(height: 24),
                  // Show current state for debugging
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Payment State: ${state.runtimeType}',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildPayButton(context, state),
                  // Add extra padding for bottom navigation
                  const SizedBox(height: 100),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBookingSummary(NumberFormat formatter) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Booking Summary',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            _buildSummaryRow('Property', widget.propertyTitle),
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Check In',
              DateFormat('dd/MM/yyyy').format(widget.checkIn),
            ),
            const SizedBox(height: 8),
            _buildSummaryRow(
              'Check Out',
              DateFormat('dd/MM/yyyy').format(widget.checkOut),
            ),
            const Divider(height: 24),
            _buildSummaryRow(
              'Total Amount',
              '${formatter.format(widget.amount)} SAR',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal 
              ? AppTextStyles.font18Bold
              : AppTextStyles.font16Regular,
        ),
        Text(
          value,
          style: isTotal 
              ? AppTextStyles.font18Bold.copyWith(color: AppColors.yellow)
              : AppTextStyles.font16Regular,
        ),
      ],
    );
  }

  Widget _buildPaymentMethods() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Payment Methods',
              style: AppTextStyles.font18Bold,
            ),
            const SizedBox(height: 16),
            _buildPaymentMethodsGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodsGrid() {
    final paymentMethods = [
      {'name': 'MADA', 'isSelected': true},
      {'name': 'Visa', 'isSelected': false},
      {'name': 'Mastercard', 'isSelected': false},
      {'name': 'Apple Pay', 'isSelected': false},
      {'name': 'STC Pay', 'isSelected': false},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: paymentMethods.map((method) {
        final isSelected = method['isSelected'] as bool;
        return GestureDetector(
          onTap: () {
            // Handle payment method selection
            print('Selected payment method: ${method['name']}');
          },
          child: Container(
            width: (MediaQuery.of(context).size.width - 80) / 3,
            height: 60,
            decoration: BoxDecoration(
              border: Border.all(
                color: isSelected
                    ? AppColors.yellow
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(8),
              color: isSelected
                  ? AppColors.yellow.withValues(alpha: 0.1)
                  : Colors.white,
            ),
            child: Center(
              child: Text(
                method['name'] as String,
                style: AppTextStyles.font14Regular.copyWith(
                  color: isSelected ? Colors.black : Colors.grey.shade600,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSecurityInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.green.shade600,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Secure Payment',
                  style: AppTextStyles.font16Bold.copyWith(
                    color: Colors.green.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Your payment information is encrypted and secure.',
                  style: AppTextStyles.font14Regular.copyWith(
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayButton(BuildContext context, PaymentState state) {
    final isLoading = state is PaymentLoading;

    print('🔄 _buildPayButton - state: $state, isLoading: $isLoading');

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: isLoading ? null : () => _processPayment(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.yellow,
          foregroundColor: Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
          elevation: 2,
        ),
        child: isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                ),
              )
            : const Text(
                'Pay Now',
                style: AppTextStyles.font18Bold,
              ),
      ),
    );
  }

  void _processPayment(BuildContext context) {
    context.read<PaymentCubit>().processPayment(
      context: context,
      reservationId: widget.reservationId,
      amount: widget.amount,
      customerEmail: widget.customerEmail,
    );
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Payment Successful!',
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Your reservation has been confirmed.',
              style: AppTextStyles.font16Regular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                  Navigator.of(context).pop(); // Close payment screen
                  Navigator.of(context).pop(); // Close booking screen
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: Colors.black,
                ),
                child: const Text('Done'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.error,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Payment Failed',
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: AppTextStyles.font16Regular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _processPayment(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.yellow,
                      foregroundColor: Colors.black,
                    ),
                    child: const Text('Retry'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelledDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.cancel,
              color: Colors.orange,
              size: 64,
            ),
            const SizedBox(height: 16),
            const Text(
              'Payment Cancelled',
              style: AppTextStyles.font20Bold,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'You cancelled the payment process.',
              style: AppTextStyles.font16Regular,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.yellow,
                  foregroundColor: Colors.black,
                ),
                child: const Text('OK'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
