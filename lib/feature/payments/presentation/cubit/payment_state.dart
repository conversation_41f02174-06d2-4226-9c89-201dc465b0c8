part of 'payment_cubit.dart';

abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

class PaymentInitial extends PaymentState {}

class PaymentLoading extends PaymentState {}

class PaymentSuccess extends PaymentState {
  final String paymentId;
  final double amount;
  final String currency;

  const PaymentSuccess({
    required this.paymentId,
    required this.amount,
    required this.currency,
  });

  @override
  List<Object?> get props => [paymentId, amount, currency];
}

class PaymentFailed extends PaymentState {
  final String message;

  const PaymentFailed(this.message);

  @override
  List<Object?> get props => [message];
}

class PaymentCancelled extends PaymentState {}

class PaymentPending extends PaymentState {
  final String paymentId;
  final String message;

  const PaymentPending({
    required this.paymentId,
    required this.message,
  });

  @override
  List<Object?> get props => [paymentId, message];
}
